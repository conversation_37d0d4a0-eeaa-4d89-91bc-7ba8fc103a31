package com.qmqb.imp.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.qmqb.imp.job.service.ProjectResultWarnService;

import cn.dev33.satoken.annotation.SaIgnore;

@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private ProjectResultWarnService projectResultWarnService;

    @GetMapping("/test")
    @SaIgnore
    public void test() {
        projectResultWarnService.projectResultWarnServiceJobHandler(null);
    }
}
